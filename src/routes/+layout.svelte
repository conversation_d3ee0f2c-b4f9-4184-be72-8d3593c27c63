<script lang="ts">
	import '../app.css';
	import '../css/bootstrap.rtl.min.css';
	import '../css/owl.theme.default.min.css';
	import '../css/owl.carousel.min.css';
	import '../css/remixicon.css';
	import '../css/flaticon.css';
	import '../css/meanmenu.min.css';
	import '../css/scrollCue.css';
	import '../css/magnific-popup.min.css';
	import '../css/odometer.min.css';
	import '../css/style.css';
	import '../css/responsive.css';
	import Header from '../components/header.svelte';
	import Footer from '../components/footer.svelte';

	import '../i18n';

	import { QueryClient, QueryClientProvider } from '@tanstack/svelte-query';
	import { browser } from '$app/environment';

	const queryClient = new QueryClient({
		defaultOptions: {
			queries: {
				enabled: browser
			}
		}
	});

	let { children } = $props();
</script>

<Header />

<QueryClientProvider client={queryClient}>
	{@render children()}
</QueryClientProvider>

<Footer />
