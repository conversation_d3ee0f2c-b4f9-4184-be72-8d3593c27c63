<script>
	let currentIndex = 0;
	const teamMembers = [
		{
			img: '/images/team/team-img-1.jpg',
			name: 'جاسپر هوگ',
			position: 'مدیریت',
			socialLinks: [
				{ platform: 'facebook', url: 'https://www.facebook.com/' },
				{ platform: 'instagram', url: 'https://www.instagram.com/' },
				{ platform: 'twitter', url: 'https://twitter.com/' },
				{ platform: 'linkedin', url: 'https://linkedin.com/' }
			]
		},
		{
			img: '/images/team/team-img-2.jpg',
			name: 'جاسپر هوگ',
			position: 'مدیریت',
			socialLinks: [
				{ platform: 'facebook', url: 'https://www.facebook.com/' },
				{ platform: 'instagram', url: 'https://www.instagram.com/' },
				{ platform: 'twitter', url: 'https://twitter.com/' },
				{ platform: 'linkedin', url: 'https://linkedin.com/' }
			]
		},
		{
			img: '/images/team/team-img-3.jpg',
			name: 'جاسپر هوگ',
			position: 'مدیریت',
			socialLinks: [
				{ platform: 'facebook', url: 'https://www.facebook.com/' },
				{ platform: 'instagram', url: 'https://www.instagram.com/' },
				{ platform: 'twitter', url: 'https://twitter.com/' },
				{ platform: 'linkedin', url: 'https://linkedin.com/' }
			]
		}
		// Add more members as needed
	];

	const goToNext = () => {
		currentIndex = (currentIndex + 1) % teamMembers.length;
	};

	const goToPrev = () => {
		currentIndex = (currentIndex - 1 + teamMembers.length) % teamMembers.length;
	};
</script>

<div class="team-slider">
	<div class="slider-container">
		{#each teamMembers as { img, name, position, socialLinks }, index}
			<div
				class="single-team-member {index === currentIndex ? 'active' : ''}"
				style="display: {index === currentIndex ? 'block' : 'none'}"
			>
				<img src={img} alt={name} />
				<div class="team-content">
					<h3>{name}</h3>
					<span>{position}</span>
					<div class="team-social">
						<a href="#" class="control">
							<i class="ri-share-line"></i>
						</a>
						<ul>
							{#each socialLinks as { platform, url }}
								<li>
									<a href={url} target="_blank">
										<i class={`ri-${platform}-fill`}></i>
									</a>
								</li>
							{/each}
						</ul>
					</div>
				</div>
			</div>
		{/each}
	</div>

	<div class="controls">
		<button on:click={goToPrev} class="prev-btn">Prev</button>
		<button on:click={goToNext} class="next-btn">Next</button>
	</div>
</div>

<style>
	.team-slider {
		position: relative;
		width: 100%;
		overflow: hidden;
	}

	.slider-container {
		display: flex;
		transition: transform 0.3s ease-in-out;
	}

	.single-team-member {
		text-align: center;
		width: 100%;
		flex-shrink: 0;
	}

	.single-team-member img {
		width: 100%;
		border-radius: 50%;
	}

	.team-content {
		text-align: center;
		margin-top: 10px;
	}

	.team-social ul {
		list-style: none;
		padding: 0;
		display: flex;
		justify-content: center;
	}

	.team-social ul li {
		margin: 0 5px;
	}

	.controls {
		position: absolute;
		top: 50%;
		width: 100%;
		display: flex;
		justify-content: space-between;
		transform: translateY(-50%);
	}

	.prev-btn,
	.next-btn {
		background: rgba(0, 0, 0, 0.5);
		color: white;
		border: none;
		padding: 10px;
		cursor: pointer;
	}

	.prev-btn:hover,
	.next-btn:hover {
		background: rgba(0, 0, 0, 0.8);
	}

	.single-team-member.active {
		opacity: 1;
		transition: opacity 0.3s ease;
	}
</style>
