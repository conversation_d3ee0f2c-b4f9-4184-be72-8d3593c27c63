<script lang="ts">
	import { onMount } from 'svelte';
	import axios from 'axios';

	let planId: number = 1;
	let weight: number = 0;
	let originCountryId: number = 0;
	let destinationCountryId: number = 0;

	let estimatedPrice: number | null = null;
	let loading = false;
	let error: string | null = null;

	async function calculatePrice() {
		loading = true;
		error = null;
		estimatedPrice = null;

		try {
			const response = await axios.get('/order/calculate', {
				params: {
					planId,
					weight,
					originCountryId,
					destinationCountryId
				}
			});

			estimatedPrice = response.data.price;
		} catch (err) {
			error = 'خطا در دریافت قیمت تخمینی';
		} finally {
			loading = false;
		}
	}
</script>

<div class="page-title-area">
	<div class="container">
		<div class="page-title-content">
			<h2>تماس با ما</h2>
			<ul>
				<li>
					<a href="/"> خانه </a>
				</li>
				<li class="active">پنل محاسبه قیمت</li>
			</ul>
		</div>
	</div>
	<div class="shape shape-1">
		<img src="/images/page-title-shape-1.png" alt="Image" />
	</div>
	<div class="shape shape-2">
		<img src="/images/page-title-shape-2.png" alt="Image" />
	</div>
</div>

<div class="price-calc-form">
	<h2>محاسبه قیمت تخمینی</h2>
	<form on:submit|preventDefault={calculatePrice}>
		<label>
			پلن:
			<input type="number" bind:value={planId} min="1" />
		</label>
		<label>
			وزن (کیلوگرم):
			<input type="number" bind:value={weight} min="0" step="0.1" />
		</label>
		<label>
			کشور مبدا:
			<input type="number" bind:value={originCountryId} />
		</label>
		<label>
			کشور مقصد:
			<input type="number" bind:value={destinationCountryId} />
		</label>
		<button type="submit" disabled={loading}>محاسبه</button>
	</form>

	{#if loading}
		<p>در حال محاسبه...</p>
	{:else if error}
		<p class="error">{error}</p>
	{:else if estimatedPrice !== null}
		<p class="result">قیمت تخمینی: {estimatedPrice.toLocaleString()} تومان</p>
	{/if}
</div>

<style>
	.price-calc-form {
		max-width: 400px;
		margin: 2rem auto;
		padding: 1rem;
		border: 1px solid #ccc;
		border-radius: 1rem;
		background-color: #f9f9f9;
	}
	form label {
		display: block;
		margin-bottom: 1rem;
	}
	input {
		width: 100%;
		padding: 0.5rem;
		border-radius: 0.5rem;
		border: 1px solid #ccc;
	}
	button {
		padding: 0.5rem 1rem;
		border: none;
		border-radius: 0.5rem;
		background-color: #007bff;
		color: white;
		cursor: pointer;
	}
	.result {
		margin-top: 1rem;
		font-weight: bold;
		color: green;
	}
	.error {
		margin-top: 1rem;
		font-weight: bold;
		color: red;
	}
</style>
